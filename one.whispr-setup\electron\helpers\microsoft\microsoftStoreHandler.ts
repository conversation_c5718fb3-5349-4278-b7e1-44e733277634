import { app } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { tmpdir } from 'os';
import { rename } from 'fs';

/**
 * Handles Microsoft Store specific logic for first launch
 */
export class MicrosoftStoreHandler {
  private appDataPath: string;
  private embeddedMainAppPath: string;
  private logFilePath: string;

  constructor() {
    // Debug: Check if we're actually detected as Microsoft Store
    const IS_MICROSOFT = process.env.IS_MICROSOFT === 'true' ||
      (typeof process !== 'undefined' && process.execPath && process.execPath.includes('WindowsApps'));
    console.log('[MICROSOFT] IS_MICROSOFT detection:', IS_MICROSOFT);
    console.log('[MICROSOFT] process.env.IS_MICROSOFT:', process.env.IS_MICROSOFT);
    console.log('[MICROSOFT] process.execPath includes WindowsApps:', process.execPath?.includes('WindowsApps'));

    // Set up log file in temp directory first for debugging
    this.logFilePath = path.join(tmpdir(), 'OneWhispr-Microsoft-Debug.log');
    this.initializeLogging();

    let userDataPath: string;
    
    if (IS_MICROSOFT) {
      // For Microsoft Store builds, we need to use the LocalState directory
      // instead of the regular userData path that Electron provides
      console.log('[MICROSOFT] Detected Microsoft Store build, getting LocalState path...');
      userDataPath = this.getMicrosoftStoreUserDataPath();
      console.log('[MICROSOFT] Using Microsoft Store LocalState path:', userDataPath);
    } else {
      // Regular build - use Electron's userData path
      userDataPath = app.getPath('userData');
      console.log('[MICROSOFT] Using regular userData path:', userDataPath);
    }

    this.appDataPath = path.join(userDataPath, 'MainApp');

    // Path to embedded main app (read-only source)
    // For Microsoft Store APPX builds with extraFiles, MainApp is in the app's content directory
    // This is one level up from resources directory, making it: app_directory/MainApp
    this.embeddedMainAppPath = path.join(path.dirname(process.resourcesPath), 'MainApp');

    console.log('[MICROSOFT] Embedded MainApp (read-only):', this.embeddedMainAppPath);
    console.log('[MICROSOFT] Working copy in local storage:', this.appDataPath);
    console.log('[MICROSOFT] This will be automatically cleaned on uninstall');
    console.log('[MICROSOFT] process.resourcesPath:', process.resourcesPath);
    console.log('[MICROSOFT] __dirname:', __dirname);
    console.log('[MICROSOFT] process.execPath:', process.execPath);

    // Log paths to file
    this.logToFile(`Embedded MainApp path: ${this.embeddedMainAppPath}`);
    this.logToFile(`Local storage path: ${this.appDataPath}`);
    this.logToFile(`Process resources path: ${process.resourcesPath}`);
    this.logToFile(`Process exec path: ${process.execPath}`);

    // Quick verification that embedded MainApp exists
    this.verifyEmbeddedMainApp();
  }

  /**
   * Initialize logging to file for debugging auto-launch issues
   */
  private initializeLogging(): void {
    try {
      const timestamp = new Date().toISOString();
      const initialLog = `\n=== OneWhispr Microsoft Store Debug Log ===\nStarted: ${timestamp}\nLog file: ${this.logFilePath}\n\n`;
      fs.writeFileSync(this.logFilePath, initialLog);
      this.logToFile('Microsoft Store Handler initialized');
      this.logToFile(`app.getPath('userData'): ${app.getPath('userData')}`);
    } catch (error) {
      console.error('[MICROSOFT] Failed to initialize logging:', error);
    }
  }

  /**
   * Log message to both console and file
   */
  private logToFile(message: string): void {
    try {
      const timestamp = new Date().toISOString();
      const logEntry = `[${timestamp}] ${message}\n`;
      console.log(`[MICROSOFT] ${message}`);
      fs.appendFileSync(this.logFilePath, logEntry);
    } catch (error) {
      console.error('[MICROSOFT] Failed to write to log file:', error);
    }
  }

  /**
   * Check if this is the first launch - need to copy from embedded to AppData
   */
  async isFirstLaunch(): Promise<boolean> {
    try {
      this.logToFile('=== Checking if this is first launch ===');

      // Check if main app exists in AppData (writable location)
      const mainAppExePath = path.join(this.appDataPath, 'One Whispr.exe');
      const resourcesPath = path.join(this.appDataPath, 'resources');
      const backendPath = path.join(this.appDataPath, 'resources', 'backend');

      const exeExists = await fs.pathExists(mainAppExePath);
      const resourcesExists = await fs.pathExists(resourcesPath);
      const backendExists = await fs.pathExists(backendPath);

      this.logToFile('AppData structure check:');
      this.logToFile(`- One Whispr.exe exists: ${exeExists}`);
      this.logToFile(`- resources/ exists: ${resourcesExists}`);
      this.logToFile(`- resources/backend/ exists: ${backendExists}`);
      this.logToFile(`- AppData path: ${this.appDataPath}`);

      // All critical components must exist for a complete installation
      const isComplete = exeExists && resourcesExists && backendExists;
      this.logToFile(`Main app in AppData complete: ${isComplete}`);
      this.logToFile(`Is first launch: ${!isComplete}`);

      return !isComplete; // First launch if not completely copied to AppData yet
    } catch (error) {
      this.logToFile(`Error checking AppData main app: ${error}`);
      return true; // Assume first launch on error
    }
  }

  /**
   * Copy MainApp from embedded resources to AppData (replaces download step for Microsoft Store)
   * This mimics the download process but copies from embedded resources instead
   */
  async copyMainAppToAppData(progressCallback?: (progress: number) => void): Promise<boolean> {
    // Add initialization delay for auto-launch scenarios to ensure paths are stable
    this.logToFile('=== Starting MainApp copy process ===');
    this.logToFile('Waiting for app initialization to complete...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      console.log('=== MICROSOFT STORE COPY DEBUG START ===');
      console.log('[MICROSOFT] Starting MainApp copy to local storage...');
      console.log('[MICROSOFT] Embedded path:', this.embeddedMainAppPath);
      console.log('[MICROSOFT] Target path:', this.appDataPath);
      console.log('[MICROSOFT] process.resourcesPath:', process.resourcesPath);
      console.log('[MICROSOFT] process.execPath:', process.execPath);
      console.log('[MICROSOFT] __dirname:', __dirname);
      console.log('[MICROSOFT] process.cwd():', process.cwd());

      // Check if embedded main app exists
      console.log('[MICROSOFT] Checking if embedded MainApp exists...');
      const embeddedExists = await fs.pathExists(this.embeddedMainAppPath);
      console.log('[MICROSOFT] Embedded MainApp exists:', embeddedExists);

      if (!embeddedExists) {
        console.log('[MICROSOFT] MainApp not found, investigating...');

        // List contents of resources directory
        try {
          console.log('[MICROSOFT] Listing resources directory contents...');
          const resourcesContents = await fs.readdir(process.resourcesPath);
          console.log('[MICROSOFT] Resources directory contents:', resourcesContents);

          // Check if there's a MainApp directory in the app's content directory (extraFiles location)
          const appContentPath = path.dirname(process.resourcesPath);
          console.log('[MICROSOFT] Checking app content directory:', appContentPath);
          const appContentExists = await fs.pathExists(appContentPath);
          console.log('[MICROSOFT] App content directory exists:', appContentExists);

          if (appContentExists) {
            const appContentContents = await fs.readdir(appContentPath);
            console.log('[MICROSOFT] App content directory contents:', appContentContents);

            // Specifically check for MainApp in the content directory
            const mainAppInContent = path.join(appContentPath, 'MainApp');
            const mainAppInContentExists = await fs.pathExists(mainAppInContent);
            console.log('[MICROSOFT] MainApp in content directory exists:', mainAppInContentExists);
          }
        } catch (err) {
          console.error('[MICROSOFT] Error investigating paths:', err);
        }

        console.error('[MICROSOFT] Embedded MainApp not found at:', this.embeddedMainAppPath);
        return false;
      }

      // Ensure local storage directory exists
      console.log('[MICROSOFT] Creating target directory...');
      console.log('[MICROSOFT] Target directory parent:', path.dirname(this.appDataPath));
      await fs.ensureDir(path.dirname(this.appDataPath));
      console.log('[MICROSOFT] Target directory created successfully');

      // Copy entire main app from embedded location to local storage with progress
      console.log('[MICROSOFT] Starting copy operation...');
      console.log('[MICROSOFT] From:', this.embeddedMainAppPath);
      console.log('[MICROSOFT] To:', this.appDataPath);

      if (progressCallback) progressCallback(10);

      // Retry logic for copy operation (handles auto-launch timing issues)
      let copySuccess = false;
      let lastError: Error | null = null;

      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          console.log(`[MICROSOFT] Copy attempt ${attempt}/3...`);
          this.logToFile(`Copy attempt ${attempt}/3 starting...`);

          // Clear target directory first to avoid partial copy issues
          if (await fs.pathExists(this.appDataPath)) {
            console.log(`[MICROSOFT] Clearing existing target directory...`);
            this.logToFile('Clearing existing target directory');
            await fs.remove(this.appDataPath);
          }

          // Create target directory
          await fs.ensureDir(this.appDataPath);

          // Copy entire MainApp directory (app.asar is renamed to app.asar.embedded in build)
          console.log(`[MICROSOFT] Copying MainApp from ${this.embeddedMainAppPath} to ${this.appDataPath}`);
          this.logToFile(`Copying MainApp from embedded location to LocalState`);
          
          await fs.copy(this.embeddedMainAppPath, this.appDataPath, {
            overwrite: true,
            errorOnExist: false
          });

          // Rename app.asar.embedded back to app.asar if it exists
          const embeddedAsarPath = path.join(this.appDataPath, 'resources', 'app.asar.embedded');
          const finalAsarPath = path.join(this.appDataPath, 'resources', 'app.asar');
          
          if (await fs.pathExists(embeddedAsarPath)) {
            console.log(`[MICROSOFT] Renaming app.asar.embedded to app.asar`);
            this.logToFile('Renaming app.asar.embedded to app.asar');
            
            // Remove existing app.asar if it exists
            if (await fs.pathExists(finalAsarPath)) {
              await fs.remove(finalAsarPath);
            }
            
            // Rename using Node.js rename to avoid package validation
            await new Promise<void>((resolve, reject) => {
              rename(embeddedAsarPath, finalAsarPath, (err: any) => {
                if (err) reject(err);
                else resolve();
              });
            });
            
            console.log(`[MICROSOFT] Successfully renamed app.asar.embedded to app.asar`);
            this.logToFile('Successfully renamed app.asar.embedded to app.asar');
          } else {
            console.log(`[MICROSOFT] app.asar.embedded not found at ${embeddedAsarPath}`);
            this.logToFile('app.asar.embedded not found - this might be normal for newer builds');
          }

          copySuccess = true;
          console.log(`[MICROSOFT] Copy successful on attempt ${attempt}`);
          this.logToFile(`Copy successful on attempt ${attempt}`);
          break;
        } catch (error) {
          lastError = error as Error;
          console.warn(`[MICROSOFT] Copy attempt ${attempt} failed:`, error);
          this.logToFile(`Copy attempt ${attempt} failed: ${error instanceof Error ? error.message : String(error)}`);
          this.logToFile(`Error details: ${JSON.stringify(error, null, 2)}`);

          if (attempt < 3) {
            console.log(`[MICROSOFT] Waiting 3 seconds before retry...`);
            this.logToFile(`Waiting 3 seconds before retry...`);
            await new Promise(resolve => setTimeout(resolve, 3000));
          }
        }
      }

      if (!copySuccess) {
        console.error('[MICROSOFT] All copy attempts failed. Last error:', lastError);
        this.logToFile(`All copy attempts failed. Last error: ${lastError?.message || 'Unknown error'}`);
        this.logToFile(`Full error details: ${JSON.stringify(lastError, null, 2)}`);
        throw lastError || new Error('Copy operation failed after 3 attempts');
      }

      if (progressCallback) progressCallback(100);

      // Verify the copy
      const mainAppExePath = path.join(this.appDataPath, 'One Whispr.exe');
      const copySuccessful = await fs.pathExists(mainAppExePath);

      if (copySuccessful) {
        console.log('[MICROSOFT] MainApp copied successfully to AppData');
        this.logToFile('MainApp copied successfully to AppData');
        return true;
      } else {
        console.error('[MICROSOFT] MainApp copy failed - exe not found after copy');
        this.logToFile('MainApp copy failed - exe not found after copy');
        this.logToFile(`Expected exe path: ${mainAppExePath}`);
        return false;
      }
    } catch (error) {
      console.error('[MICROSOFT] Error copying MainApp to AppData:', error);
      this.logToFile(`Error copying MainApp to AppData: ${error instanceof Error ? error.message : String(error)}`);
      this.logToFile(`Full error details: ${JSON.stringify(error, null, 2)}`);
      return false;
    }
  }



  /**
   * Legacy method for compatibility - now just calls copyMainAppToAppData
   */
  async extractMainAppToAppData(): Promise<boolean> {
    return this.copyMainAppToAppData();
  }

  /**
   * Get the path where main app should be launched from (AppData)
   */
  getMainAppPath(): string {
    return path.join(this.appDataPath, 'One Whispr.exe');
  }

  /**
   * Get the backend path in AppData (where runtime should be extracted)
   */
  getBackendPath(): string {
    return path.join(this.appDataPath, 'resources', 'backend');
  }

  /**
   * Get the embedded MainApp path (read-only source)
   */
  getEmbeddedMainAppPath(): string {
    return this.embeddedMainAppPath;
  }

  /**
   * Verify that the embedded MainApp exists and log details
   */
  private async verifyEmbeddedMainApp(): Promise<void> {
    try {
      this.logToFile('=== Verifying embedded MainApp ===');
      const exists = await fs.pathExists(this.embeddedMainAppPath);
      this.logToFile(`Embedded MainApp exists: ${exists}`);

      if (exists) {
        const stats = await fs.stat(this.embeddedMainAppPath);
        this.logToFile(`Embedded MainApp is directory: ${stats.isDirectory()}`);

        if (stats.isDirectory()) {
          const contents = await fs.readdir(this.embeddedMainAppPath);
          this.logToFile(`Embedded MainApp contents (first 10): ${contents.slice(0, 10).join(', ')}`);
        }
      } else {
        // Check what's actually in resources
        const resourcesExists = await fs.pathExists(process.resourcesPath);
        this.logToFile(`Resources directory exists: ${resourcesExists}`);

        if (resourcesExists) {
          const resourcesContents = await fs.readdir(process.resourcesPath);
          this.logToFile(`Resources directory contents: ${resourcesContents.join(', ')}`);
        }
      }
    } catch (error) {
      this.logToFile(`Error verifying embedded MainApp: ${error}`);
    }
  }

  /**
   * Get the embedded runtime path (source for extraction)
   */
  getEmbeddedRuntimePath(): string {
    return path.join(this.embeddedMainAppPath, 'resources', 'backend', 'OneWhispr-Runtime-Base.7z');
  }

  /**
   * Get the embedded msstore.json path (contains version info outside 7z archives)
   */
  getEmbeddedMsstorePath(): string {
    return path.join(this.embeddedMainAppPath, 'resources', 'backend', 'msstore.json');
  }

  /**
   * Get the local runtime-version.json path (in AppData backend)
   */
  getLocalRuntimeVersionPath(): string {
    return path.join(this.getBackendPath(), 'runtime-version.json');
  }

  /**
   * Check if embedded runtime exists
   */
  async hasEmbeddedRuntime(): Promise<boolean> {
    try {
      const runtimePath = this.getEmbeddedRuntimePath();
      const exists = await fs.pathExists(runtimePath);
      console.log('[MICROSOFT] Embedded runtime exists:', exists, 'at', runtimePath);
      return exists;
    } catch (error) {
      console.error('[MICROSOFT] Error checking embedded runtime:', error);
      return false;
    }
  }

  /**
   * Check if embedded runtime is newer than local runtime
   */
  async isEmbeddedRuntimeNewer(): Promise<{ isNewer: boolean, reason: string, embeddedVersion?: string, localVersion?: string }> {
    try {
      this.logToFile('=== Checking if embedded runtime is newer ===');

      // Check if embedded msstore.json exists
      const embeddedMsstorePath = this.getEmbeddedMsstorePath();
      const embeddedMsstoreExists = await fs.pathExists(embeddedMsstorePath);

      if (!embeddedMsstoreExists) {
        this.logToFile(`Embedded msstore.json not found at: ${embeddedMsstorePath}`);
        return { isNewer: false, reason: 'No embedded msstore.json found' };
      }

      // Read embedded msstore.json
      const embeddedMsstore = await fs.readJson(embeddedMsstorePath);
      const embeddedVersion = embeddedMsstore.runtime?.version;
      const embeddedReleaseDate = embeddedMsstore.runtime?.releaseDate;

      if (!embeddedVersion || !embeddedReleaseDate) {
        this.logToFile('Embedded msstore.json missing runtime version/date');
        return { isNewer: false, reason: 'Invalid embedded msstore.json' };
      }

      this.logToFile(`Embedded runtime version: ${embeddedVersion} (${embeddedReleaseDate})`);

      // Check local runtime-version.json
      const localRuntimeVersionPath = this.getLocalRuntimeVersionPath();
      const localRuntimeVersionExists = await fs.pathExists(localRuntimeVersionPath);

      if (!localRuntimeVersionExists) {
        this.logToFile('No local runtime-version.json found - embedded is newer');
        return {
          isNewer: true,
          reason: 'No local runtime installed',
          embeddedVersion,
          localVersion: 'none'
        };
      }

      // Read local runtime-version.json
      const localRuntimeVersion = await fs.readJson(localRuntimeVersionPath);
      const localVersion = localRuntimeVersion.version;
      const localReleaseDate = localRuntimeVersion.releaseDate;

      if (!localVersion || !localReleaseDate) {
        this.logToFile('Local runtime-version.json missing version/date');
        return {
          isNewer: true,
          reason: 'Invalid local runtime-version.json',
          embeddedVersion,
          localVersion: 'invalid'
        };
      }

      this.logToFile(`Local runtime version: ${localVersion} (${localReleaseDate})`);

      // Compare release dates
      const embeddedDate = new Date(embeddedReleaseDate);
      const localDate = new Date(localReleaseDate);

      const isNewer = embeddedDate > localDate;
      const reason = isNewer
        ? `Embedded runtime is newer (${embeddedReleaseDate} > ${localReleaseDate})`
        : `Local runtime is up to date (${localReleaseDate} >= ${embeddedReleaseDate})`;

      this.logToFile(reason);

      return {
        isNewer,
        reason,
        embeddedVersion,
        localVersion
      };

    } catch (error) {
      this.logToFile(`Error checking runtime versions: ${error}`);
      return { isNewer: false, reason: `Error: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * Check if embedded runtime is newer than local runtime
   */
  async isEmbeddedRuntimeNewer(): Promise<{ isNewer: boolean, reason: string, embeddedVersion?: string, localVersion?: string }> {
    try {
      this.logToFile('=== Checking if embedded runtime is newer ===');

      // Check if embedded status.json exists
      const embeddedStatusPath = this.getEmbeddedStatusPath();
      const embeddedStatusExists = await fs.pathExists(embeddedStatusPath);

      if (!embeddedStatusExists) {
        this.logToFile(`Embedded status.json not found at: ${embeddedStatusPath}`);
        return { isNewer: false, reason: 'No embedded status.json found' };
      }

      // Read embedded status
      const embeddedStatus = await fs.readJson(embeddedStatusPath);
      const embeddedVersion = embeddedStatus.runtime?.version;
      const embeddedReleaseDate = embeddedStatus.runtime?.releaseDate;

      if (!embeddedVersion || !embeddedReleaseDate) {
        this.logToFile('Embedded status.json missing runtime version/date');
        return { isNewer: false, reason: 'Invalid embedded status.json' };
      }

      this.logToFile(`Embedded runtime version: ${embeddedVersion} (${embeddedReleaseDate})`);

      // Check local status
      const localStatusPath = this.getLocalStatusPath();
      const localStatusExists = await fs.pathExists(localStatusPath);

      if (!localStatusExists) {
        this.logToFile('No local status.json found - embedded is newer');
        return {
          isNewer: true,
          reason: 'No local runtime installed',
          embeddedVersion,
          localVersion: 'none'
        };
      }

      // Read local status
      const localStatus = await fs.readJson(localStatusPath);
      const localVersion = localStatus.runtime?.version;
      const localReleaseDate = localStatus.runtime?.releaseDate;

      if (!localVersion || !localReleaseDate) {
        this.logToFile('Local status.json missing runtime version/date');
        return {
          isNewer: true,
          reason: 'Invalid local status.json',
          embeddedVersion,
          localVersion: 'invalid'
        };
      }

      this.logToFile(`Local runtime version: ${localVersion} (${localReleaseDate})`);

      // Compare release dates
      const embeddedDate = new Date(embeddedReleaseDate);
      const localDate = new Date(localReleaseDate);

      const isNewer = embeddedDate > localDate;
      const reason = isNewer
        ? `Embedded runtime is newer (${embeddedReleaseDate} > ${localReleaseDate})`
        : `Local runtime is up to date (${localReleaseDate} >= ${embeddedReleaseDate})`;

      this.logToFile(reason);

      return {
        isNewer,
        reason,
        embeddedVersion,
        localVersion
      };

    } catch (error) {
      this.logToFile(`Error checking runtime versions: ${error}`);
      return { isNewer: false, reason: `Error: ${error instanceof Error ? error.message : String(error)}` };
    }
  }

  /**
   * Update local runtime from embedded runtime (when embedded is newer)
   */
  async updateRuntimeFromEmbedded(progressCallback?: (progress: number) => void): Promise<boolean> {
    try {
      this.logToFile('=== Updating runtime from embedded ===');
      console.log('[MICROSOFT] Updating runtime from embedded version...');

      if (progressCallback) progressCallback(10);

      // Get paths
      const embeddedRuntimePath = this.getEmbeddedRuntimePath();
      const localBackendPath = this.getBackendPath();
      const embeddedMsstorePath = this.getEmbeddedMsstorePath();

      // Verify embedded runtime exists
      if (!await fs.pathExists(embeddedRuntimePath)) {
        this.logToFile('Embedded runtime not found');
        return false;
      }

      if (progressCallback) progressCallback(20);

      // Clear existing runtime files (but preserve scripts)
      this.logToFile('Clearing existing runtime files...');
      const runtimeFiles = ['One Whispr Backend.exe', '_internal', 'runtime-version.json'];

      for (const file of runtimeFiles) {
        const filePath = path.join(localBackendPath, file);
        if (await fs.pathExists(filePath)) {
          await fs.remove(filePath);
          this.logToFile(`Removed: ${file}`);
        }
      }

      if (progressCallback) progressCallback(40);

      // Extract embedded runtime to local backend
      this.logToFile('Extracting embedded runtime...');
      const { ArchiveExtractor } = await import('../backend/archiveExtractor');
      const extractor = new ArchiveExtractor(localBackendPath);

      await extractor.extract7z(embeddedRuntimePath, localBackendPath);

      if (progressCallback) progressCallback(80);

      // Copy version information from embedded msstore.json to local runtime-version.json
      if (await fs.pathExists(embeddedMsstorePath)) {
        const embeddedMsstore = await fs.readJson(embeddedMsstorePath);
        const runtimeInfo = embeddedMsstore.runtime;

        if (runtimeInfo) {
          const localRuntimeVersionPath = this.getLocalRuntimeVersionPath();
          await fs.writeJson(localRuntimeVersionPath, {
            version: runtimeInfo.version,
            releaseDate: runtimeInfo.releaseDate,
            releaseNotes: runtimeInfo.releaseNotes,
            installedAt: new Date().toISOString(),
            source: 'microsoft-store-embedded'
          });
          this.logToFile(`Created runtime-version.json with version ${runtimeInfo.version}`);
        }
      }

      if (progressCallback) progressCallback(100);

      this.logToFile('Runtime update from embedded completed successfully');
      console.log('[MICROSOFT] Runtime updated from embedded version successfully');
      return true;

    } catch (error) {
      this.logToFile(`Error updating runtime from embedded: ${error}`);
      console.error('[MICROSOFT] Error updating runtime from embedded:', error);
      return false;
    }
  }

  /**
   * Handle Microsoft Store first launch setup - copy embedded resources to AppData
   */
  async handleFirstLaunch(): Promise<boolean> {
    try {
      console.log('[MICROSOFT] Starting Microsoft Store first launch setup...');

      // Check if this is actually a first launch
      const isFirst = await this.isFirstLaunch();
      if (!isFirst) {
        console.log('[MICROSOFT] Not a first launch - main app already exists in AppData');
        return true;
      }

      // Extract main app to AppData
      const extractionSuccess = await this.extractMainAppToAppData();
      if (!extractionSuccess) {
        console.error('[MICROSOFT] Failed to extract main app to AppData');
        return false;
      }

      console.log('[MICROSOFT] First launch setup completed successfully');
      return true;
    } catch (error) {
      console.error('[MICROSOFT] Error during first launch setup:', error);
      return false;
    }
  }

  /**
   * Get the proper Microsoft Store LocalState path
   */
  private getMicrosoftStoreUserDataPath(): string {
    try {
      console.log('[MICROSOFT] Finding OneWhispr package in LocalState...');
      this.logToFile('Finding OneWhispr package in LocalState');
      
      const username = process.env.USERNAME || process.env.USER;
      const packagesDir = `C:\\Users\\<USER>\\AppData\\Local\\Packages`;
      
      if (fs.existsSync(packagesDir)) {
        const packages = fs.readdirSync(packagesDir);
        const oneWhisprPackage = packages.find(pkg => pkg.includes('OneWhispr'));
        
        if (oneWhisprPackage) {
          const localStatePath = path.join(packagesDir, oneWhisprPackage, 'LocalState');
          console.log('[MICROSOFT] Found package:', oneWhisprPackage);
          console.log('[MICROSOFT] LocalState path:', localStatePath);
          this.logToFile(`Found package: ${oneWhisprPackage}`);
          this.logToFile(`LocalState path: ${localStatePath}`);
          
          // Create the directory if it doesn't exist
          fs.ensureDirSync(localStatePath);
          return localStatePath;
        }
      }
      
      console.log('[MICROSOFT] OneWhispr package not found, using regular userData');
      this.logToFile('OneWhispr package not found, using regular userData');
      return app.getPath('userData');
      
    } catch (error) {
      console.error('[MICROSOFT] Error finding LocalState path:', error);
      this.logToFile(`Error finding LocalState path: ${error}`);
      return app.getPath('userData');
    }
  }


}

// Lazy-initialized singleton instance - only created when first accessed
let _microsoftStoreHandler: MicrosoftStoreHandler | null = null;

export const microsoftStoreHandler = {
  get instance(): MicrosoftStoreHandler {
    if (!_microsoftStoreHandler) {
      console.log('[MICROSOFT] Initializing Microsoft Store Handler...');
      _microsoftStoreHandler = new MicrosoftStoreHandler();
    }
    return _microsoftStoreHandler;
  }
};

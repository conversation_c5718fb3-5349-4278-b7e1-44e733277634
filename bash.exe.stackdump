Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB750, 0007FFFFA650) msys-2.0.dll+0x1FEBA
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210285FF9, 0007FFFFB608, 0007FFFFB750, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB750  0002100690B4 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA30  00021006A49D (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB91CA0000 ntdll.dll
7FFB90AD0000 KERNEL32.DLL
7FFB8F270000 KERNELBASE.dll
7FFB90D70000 USER32.dll
7FFB8F6A0000 win32u.dll
7FFB91120000 GDI32.dll
7FFB8F6D0000 gdi32full.dll
7FFB8F600000 msvcp_win.dll
7FFB8F9D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB90C10000 advapi32.dll
7FFB90540000 msvcrt.dll
7FFB904A0000 sechost.dll
7FFB91350000 RPCRT4.dll
7FFB8E980000 CRYPTBASE.DLL
7FFB8F130000 bcryptPrimitives.dll
7FFB90F30000 IMM32.DLL
